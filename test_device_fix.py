#!/usr/bin/env python3
"""
测试设备自动检测修复
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.detection.detector import YOLODetector
    from src.utils import setup_logger
    from loguru import logger
    import torch
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)


def test_device_detection():
    """测试设备自动检测功能"""
    setup_logger(level="INFO")
    
    print("🔍 测试设备自动检测功能")
    print("=" * 50)
    
    # 显示PyTorch CUDA信息
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"CUDA设备数: {torch.cuda.device_count()}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    print("\n🤖 测试YOLO检测器设备自动检测...")
    
    # 测试不同的设备设置
    test_cases = ['auto', 'cpu', 'cuda']
    
    for device in test_cases:
        print(f"\n📋 测试设备设置: {device}")
        try:
            detector = YOLODetector(
                model_path="scripts/models/yolov8s.pt",
                conf_threshold=0.5,
                device=device
            )
            print(f"✅ 设备 '{device}' 测试成功，实际使用: {detector.device}")
        except Exception as e:
            print(f"❌ 设备 '{device}' 测试失败: {e}")
    
    print("\n🎉 设备检测测试完成!")


if __name__ == "__main__":
    test_device_detection()
