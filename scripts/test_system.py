#!/usr/bin/env python3
"""
系统测试脚本
用于测试各个模块的功能
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config import ConfigManager
from src.utils import setup_logger, calculate_iou
from src.detection import YOLODetector, VideoCapture
from src.state_machine import ActionStateMachine, ActionState
from loguru import logger


def test_config_manager():
    """测试配置管理器"""
    logger.info("🧪 测试配置管理器...")
    
    try:
        config = ConfigManager("config.yaml")
        
        # 测试基本配置读取
        assert config.model_path is not None
        assert config.conf_threshold > 0
        assert config.iou_held > 0
        
        logger.success("✅ 配置管理器测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 配置管理器测试失败: {e}")
        return False


def test_geometry_utils():
    """测试几何工具函数"""
    logger.info("🧪 测试几何工具函数...")
    
    try:
        # 测试IoU计算
        box1 = [0, 0, 10, 10]  # 面积100
        box2 = [5, 5, 15, 15]  # 面积100，交集25
        
        iou = calculate_iou(box1, box2)
        expected_iou = 25 / (100 + 100 - 25)  # 交集/并集
        
        assert abs(iou - expected_iou) < 0.001, f"IoU计算错误: {iou} != {expected_iou}"
        
        # 测试无交集情况
        box3 = [20, 20, 30, 30]
        iou_no_overlap = calculate_iou(box1, box3)
        assert iou_no_overlap == 0, f"无交集IoU应为0: {iou_no_overlap}"
        
        # 测试完全重叠情况
        iou_complete = calculate_iou(box1, box1)
        assert abs(iou_complete - 1.0) < 0.001, f"完全重叠IoU应为1: {iou_complete}"
        
        logger.success("✅ 几何工具函数测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 几何工具函数测试失败: {e}")
        return False


def test_state_machine():
    """测试状态机"""
    logger.info("🧪 测试状态机...")
    
    try:
        # 创建状态机
        sm = ActionStateMachine(
            iou_held_threshold=0.3,
            iou_in_container_threshold=0.5,
            iou_release_threshold=0.2,
            cooldown_seconds=1.0
        )
        
        # 测试初始状态
        assert sm.current_state == ActionState.IDLE
        
        # 模拟检测结果
        detections_empty = {'hand': [], 'product': [], 'target_container': []}
        detections_hand_product = {
            'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],
            'product': [{'bbox': [12, 12, 18, 18], 'conf': 0.8}],  # 与手重叠
            'target_container': []
        }
        detections_in_container = {
            'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],
            'product': [{'bbox': [50, 50, 60, 60], 'conf': 0.8}],
            'target_container': [{'bbox': [45, 45, 65, 65], 'conf': 0.9}]  # 产品在容器内
        }
        detections_released = {
            'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],  # 手远离产品
            'product': [{'bbox': [50, 50, 60, 60], 'conf': 0.8}],
            'target_container': [{'bbox': [45, 45, 65, 65], 'conf': 0.9}]
        }
        
        # 测试状态转移
        # 1. 空闲 -> 跟踪
        result = sm.update(detections_hand_product)
        assert sm.current_state == ActionState.TRACKING_HELD_OBJECT
        assert result is None
        
        # 2. 跟踪 -> 评估
        result = sm.update(detections_in_container)
        assert sm.current_state == ActionState.EVALUATING_RELEASE
        assert result is None
        
        # 等待最小跟踪时间
        time.sleep(0.6)
        
        # 3. 评估 -> 动作完成
        result = sm.update(detections_released)
        assert sm.current_state == ActionState.IDLE
        assert result is not None
        assert result['action_id'] == 1
        
        logger.success("✅ 状态机测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 状态机测试失败: {e}")
        return False


def test_video_capture():
    """测试视频捕获（如果有摄像头）"""
    logger.info("🧪 测试视频捕获...")
    
    try:
        # 尝试打开摄像头
        cap = VideoCapture(0)
        
        if not cap.is_opened():
            logger.warning("⚠️ 未检测到摄像头，跳过视频捕获测试")
            return True
        
        # 读取几帧测试
        for i in range(3):
            success, frame = cap.read_frame()
            if not success:
                logger.warning(f"⚠️ 第{i+1}帧读取失败")
                break
            
            assert frame is not None
            assert len(frame.shape) == 3  # 应该是彩色图像
        
        cap.release()
        logger.success("✅ 视频捕获测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 视频捕获测试失败: {e}")
        return False


def test_yolo_detector():
    """测试YOLO检测器"""
    logger.info("🧪 测试YOLO检测器...")
    
    try:
        # 创建检测器（使用预训练模型）
        detector = YOLODetector(
            model_path="yolov8n.pt",  # 将自动下载
            conf_threshold=0.5
        )
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 执行检测
        detections = detector.detect(test_image)
        
        # 验证返回格式
        assert isinstance(detections, dict)
        assert 'hand' in detections
        assert 'product' in detections
        assert 'target_container' in detections
        
        logger.success("✅ YOLO检测器测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ YOLO检测器测试失败: {e}")
        logger.warning("这可能是因为网络问题或缺少依赖，请检查ultralytics安装")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("=" * 60)
    logger.info("🚀 开始系统测试")
    logger.info("=" * 60)
    
    tests = [
        ("配置管理器", test_config_manager),
        ("几何工具函数", test_geometry_utils),
        ("状态机", test_state_machine),
        ("视频捕获", test_video_capture),
        ("YOLO检测器", test_yolo_detector),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            logger.success(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    # 汇总结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果汇总")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.success("🎉 所有测试通过！系统准备就绪")
        return 0
    else:
        logger.warning(f"⚠️ {total - passed} 个测试失败，请检查相关模块")
        return 1


def main():
    """主函数"""
    # 设置日志
    setup_logger(level="INFO")
    
    return run_all_tests()


if __name__ == "__main__":
    sys.exit(main())
