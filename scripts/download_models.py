#!/usr/bin/env python3
"""
YOLOv8预训练模型下载脚本
"""

import os
import sys
import requests
from pathlib import Path
from tqdm import tqdm
import argparse

# 预训练模型信息
YOLO_MODELS = {
    'yolov8n': {
        'url': 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt',
        'size': '6.2MB',
        'description': 'YOLOv8 Nano - 最小最快的模型'
    },
    'yolov8s': {
        'url': 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt',
        'size': '21.5MB', 
        'description': 'YOLOv8 Small - 速度和精度平衡'
    },
    'yolov8m': {
        'url': 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt',
        'size': '49.7MB',
        'description': 'YOLOv8 Medium - 中等大小模型'
    },
    'yolov8l': {
        'url': 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8l.pt', 
        'size': '83.7MB',
        'description': 'YOLOv8 Large - 大型高精度模型'
    },
    'yolov8x': {
        'url': 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8x.pt',
        'size': '136.7MB',
        'description': 'YOLOv8 Extra Large - 最大最准确的模型'
    }
}

# 国内镜像源（如果GitHub下载慢）
MIRROR_URLS = {
    'yolov8n': 'https://download.pytorch.org/models/yolov8n.pt',
    'yolov8s': 'https://download.pytorch.org/models/yolov8s.pt',
    'yolov8m': 'https://download.pytorch.org/models/yolov8m.pt',
    'yolov8l': 'https://download.pytorch.org/models/yolov8l.pt',
    'yolov8x': 'https://download.pytorch.org/models/yolov8x.pt',
}


def download_file(url: str, filepath: Path, use_mirror: bool = False) -> bool:
    """
    下载文件并显示进度条
    
    Args:
        url: 下载链接
        filepath: 保存路径
        use_mirror: 是否使用镜像源
        
    Returns:
        是否下载成功
    """
    try:
        print(f"📥 开始下载: {filepath.name}")
        print(f"🔗 下载链接: {url}")
        
        # 发送请求
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))
        
        # 创建目录
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # 下载文件
        with open(filepath, 'wb') as f, tqdm(
            desc=filepath.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✅ 下载完成: {filepath}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if filepath.exists():
            filepath.unlink()  # 删除不完整的文件
        return False


def download_model(model_name: str, output_dir: Path, use_mirror: bool = False) -> bool:
    """
    下载指定的模型
    
    Args:
        model_name: 模型名称
        output_dir: 输出目录
        use_mirror: 是否使用镜像源
        
    Returns:
        是否下载成功
    """
    if model_name not in YOLO_MODELS:
        print(f"❌ 未知模型: {model_name}")
        print(f"可用模型: {list(YOLO_MODELS.keys())}")
        return False
    
    model_info = YOLO_MODELS[model_name]
    filepath = output_dir / f"{model_name}.pt"
    
    # 检查文件是否已存在
    if filepath.exists():
        print(f"⚠️  文件已存在: {filepath}")
        response = input("是否重新下载? (y/N): ").lower()
        if response != 'y':
            print("跳过下载")
            return True
    
    # 选择下载链接
    if use_mirror and model_name in MIRROR_URLS:
        url = MIRROR_URLS[model_name]
        print("🌐 使用镜像源下载")
    else:
        url = model_info['url']
    
    print(f"📋 模型信息: {model_info['description']} ({model_info['size']})")
    
    return download_file(url, filepath, use_mirror)


def list_models():
    """列出所有可用的模型"""
    print("📋 可用的YOLOv8预训练模型:")
    print("=" * 60)
    
    for name, info in YOLO_MODELS.items():
        print(f"🤖 {name}.pt")
        print(f"   大小: {info['size']}")
        print(f"   描述: {info['description']}")
        print()


def download_with_ultralytics(model_name: str, output_dir: Path) -> bool:
    """
    使用ultralytics库下载模型（推荐方法）
    
    Args:
        model_name: 模型名称
        output_dir: 输出目录
        
    Returns:
        是否下载成功
    """
    try:
        from ultralytics import YOLO
        
        print(f"📥 使用ultralytics下载: {model_name}")
        
        # 创建YOLO对象，这会自动下载模型
        model = YOLO(f'{model_name}.pt')
        
        # 获取模型文件路径
        model_path = model.ckpt_path
        target_path = output_dir / f"{model_name}.pt"
        
        # 如果模型不在目标目录，复制过去
        if Path(model_path) != target_path:
            import shutil
            output_dir.mkdir(parents=True, exist_ok=True)
            shutil.copy2(model_path, target_path)
            print(f"📁 模型已复制到: {target_path}")
        
        print(f"✅ 下载完成: {model_name}.pt")
        return True
        
    except ImportError:
        print("❌ ultralytics未安装，请先安装: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="YOLOv8预训练模型下载工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python download_models.py --list                    # 列出所有模型
  python download_models.py --model yolov8n           # 下载nano模型
  python download_models.py --model yolov8s --mirror  # 使用镜像源下载
  python download_models.py --all                     # 下载所有模型
  python download_models.py --ultralytics yolov8n     # 使用ultralytics下载
        """
    )
    
    parser.add_argument('--list', action='store_true', help='列出所有可用模型')
    parser.add_argument('--model', type=str, help='要下载的模型名称')
    parser.add_argument('--all', action='store_true', help='下载所有模型')
    parser.add_argument('--output', '-o', type=str, default='models', help='输出目录')
    parser.add_argument('--mirror', action='store_true', help='使用镜像源下载')
    parser.add_argument('--ultralytics', type=str, help='使用ultralytics库下载指定模型')
    
    args = parser.parse_args()
    
    if args.list:
        list_models()
        return 0
    
    output_dir = Path(args.output)
    
    if args.ultralytics:
        success = download_with_ultralytics(args.ultralytics, output_dir)
        return 0 if success else 1
    
    if args.all:
        print("📦 下载所有YOLOv8模型...")
        success_count = 0
        for model_name in YOLO_MODELS.keys():
            if download_model(model_name, output_dir, args.mirror):
                success_count += 1
        
        print(f"\n📊 下载完成: {success_count}/{len(YOLO_MODELS)} 个模型")
        return 0 if success_count == len(YOLO_MODELS) else 1
    
    if args.model:
        success = download_model(args.model, output_dir, args.mirror)
        return 0 if success else 1
    
    # 如果没有指定参数，显示帮助
    parser.print_help()
    return 0


if __name__ == "__main__":
    sys.exit(main())
