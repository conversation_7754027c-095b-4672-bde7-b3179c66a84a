#!/bin/bash
# YOLOv8实时动作检测系统环境设置脚本

set -e  # 遇到错误立即退出

echo "🚀 YOLOv8实时动作检测系统环境设置"
echo "=================================="

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: conda未安装，请先安装Anaconda或Miniconda"
    exit 1
fi

# 环境名称
ENV_NAME="yolo_detect"

# 检查环境是否已存在
if conda env list | grep -q "^${ENV_NAME} "; then
    echo "⚠️  环境 ${ENV_NAME} 已存在"
    read -p "是否删除并重新创建? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        conda env remove -n ${ENV_NAME} -y
    else
        echo "📦 激活现有环境..."
        conda activate ${ENV_NAME}
        echo "✅ 环境已激活: ${ENV_NAME}"
        exit 0
    fi
fi

# 创建新环境
echo "📦 创建conda环境: ${ENV_NAME}"
conda create -n ${ENV_NAME} python=3.9 -y

# 激活环境
echo "🔄 激活环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

# 检查是否在正确的环境中
if [[ "$CONDA_DEFAULT_ENV" != "$ENV_NAME" ]]; then
    echo "❌ 错误: 环境激活失败"
    exit 1
fi

echo "✅ 环境激活成功: $CONDA_DEFAULT_ENV"

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装依赖包
echo "📦 安装Python依赖包..."

# 使用清华镜像源加速下载
PIP_INDEX="https://pypi.tuna.tsinghua.edu.cn/simple"

# 核心依赖
echo "  安装核心依赖..."
pip install -i ${PIP_INDEX} ultralytics>=8.0.0
pip install -i ${PIP_INDEX} opencv-python>=4.8.0
pip install -i ${PIP_INDEX} numpy>=1.24.0
pip install -i ${PIP_INDEX} pandas>=2.0.0
pip install -i ${PIP_INDEX} Pillow>=9.5.0

# 配置和日志
echo "  安装配置和日志工具..."
pip install -i ${PIP_INDEX} PyYAML>=6.0
pip install -i ${PIP_INDEX} loguru>=0.7.0
pip install -i ${PIP_INDEX} tqdm>=4.65.0

# 可视化工具
echo "  安装可视化工具..."
pip install -i ${PIP_INDEX} matplotlib>=3.7.0
pip install -i ${PIP_INDEX} seaborn>=0.12.0

# 开发工具（可选）
echo "  安装开发工具..."
pip install -i ${PIP_INDEX} pytest>=7.4.0
pip install -i ${PIP_INDEX} black>=23.0.0
pip install -i ${PIP_INDEX} flake8>=6.0.0

# 验证安装
echo "🔍 验证安装..."

# 检查关键包
python -c "import ultralytics; print(f'✅ ultralytics: {ultralytics.__version__}')" || echo "❌ ultralytics安装失败"
python -c "import cv2; print(f'✅ opencv-python: {cv2.__version__}')" || echo "❌ opencv-python安装失败"
python -c "import torch; print(f'✅ torch: {torch.__version__}')" || echo "❌ torch安装失败"
python -c "import numpy; print(f'✅ numpy: {numpy.__version__}')" || echo "❌ numpy安装失败"
python -c "import yaml; print('✅ PyYAML安装成功')" || echo "❌ PyYAML安装失败"
python -c "from loguru import logger; print('✅ loguru安装成功')" || echo "❌ loguru安装失败"

# 创建激活脚本
ACTIVATE_SCRIPT="activate_env.sh"
cat > ${ACTIVATE_SCRIPT} << EOF
#!/bin/bash
# 激活YOLOv8检测环境的便捷脚本

echo "🔄 激活YOLOv8检测环境..."
source \$(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

if [[ "\$CONDA_DEFAULT_ENV" == "${ENV_NAME}" ]]; then
    echo "✅ 环境已激活: \$CONDA_DEFAULT_ENV"
    echo "💡 现在可以运行以下命令:"
    echo "   python main.py                    # 运行主程序"
    echo "   python train_model.py             # 训练模型"
    echo "   python scripts/test_system.py     # 测试系统"
    echo "   python scripts/prepare_dataset.py # 准备数据集"
else
    echo "❌ 环境激活失败"
    exit 1
fi
EOF

chmod +x ${ACTIVATE_SCRIPT}

echo ""
echo "🎉 环境设置完成！"
echo "=================================="
echo "📝 使用说明:"
echo "1. 激活环境: source ${ACTIVATE_SCRIPT}"
echo "2. 或手动激活: conda activate ${ENV_NAME}"
echo "3. 运行测试: python scripts/test_system.py"
echo "4. 运行主程序: python main.py"
echo ""
echo "📁 项目结构:"
echo "├── main.py              # 主程序入口"
echo "├── train_model.py       # 模型训练脚本"
echo "├── config.yaml          # 配置文件"
echo "├── data.yaml            # 数据集配置"
echo "├── requirements.txt     # Python依赖"
echo "├── src/                 # 源代码"
echo "├── scripts/             # 辅助脚本"
echo "├── models/              # 模型文件"
echo "├── data/                # 数据集"
echo "├── logs/                # 日志文件"
echo "└── output/              # 输出结果"
echo ""
echo "🔗 相关链接:"
echo "- YOLOv8文档: https://docs.ultralytics.com/"
echo "- OpenCV文档: https://docs.opencv.org/"
echo ""
echo "✨ 祝您使用愉快！"
