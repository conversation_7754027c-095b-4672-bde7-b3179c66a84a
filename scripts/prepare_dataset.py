#!/usr/bin/env python3
"""
数据集准备脚本
帮助用户准备和验证YOLO格式的数据集
"""

import argparse
import shutil
import json
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger


def create_sample_dataset():
    """创建示例数据集结构"""
    logger.info("创建示例数据集结构...")
    
    # 创建目录结构
    dirs = [
        "data/train/images",
        "data/train/labels",
        "data/val/images", 
        "data/val/labels"
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")
    
    # 创建README文件
    readme_content = """# 数据集说明

## 目录结构
```
data/
├── train/
│   ├── images/  # 训练图像 (.jpg, .png, .jpeg)
│   └── labels/  # 训练标签 (.txt, YOLO格式)
└── val/
    ├── images/  # 验证图像
    └── labels/  # 验证标签
```

## 标签格式 (YOLO格式)
每个图像对应一个同名的.txt标签文件，格式为：
```
class_id center_x center_y width height
```

其中：
- class_id: 类别ID (0=hand, 1=product, 2=target_container)
- center_x, center_y: 边界框中心点坐标 (相对于图像尺寸，范围0-1)
- width, height: 边界框宽高 (相对于图像尺寸，范围0-1)

## 示例标签文件内容
```
0 0.5 0.3 0.2 0.4    # hand类别，中心点(0.5, 0.3)，宽0.2，高0.4
1 0.7 0.6 0.15 0.25  # product类别
2 0.8 0.8 0.3 0.3    # target_container类别
```

## 数据收集建议
1. 收集不同光照条件下的图像
2. 包含不同角度和距离的拍摄
3. 确保三个类别都有足够的样本
4. 建议训练集:验证集 = 8:2
5. 每个类别至少100张图像

## 标注工具推荐
- labelImg: https://github.com/tzutalin/labelImg
- Roboflow: https://roboflow.com/
- CVAT: https://github.com/openvinotoolkit/cvat
"""
    
    readme_path = Path("data/README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    logger.success(f"数据集结构创建完成，说明文档: {readme_path}")


def validate_yolo_labels(label_dir: Path, image_dir: Path) -> Dict[str, Any]:
    """验证YOLO格式标签"""
    logger.info(f"验证标签目录: {label_dir}")
    
    stats = {
        'total_labels': 0,
        'total_objects': 0,
        'class_counts': {0: 0, 1: 0, 2: 0},  # hand, product, target_container
        'invalid_files': [],
        'missing_images': [],
        'missing_labels': []
    }
    
    # 检查标签文件
    label_files = list(label_dir.glob("*.txt"))
    stats['total_labels'] = len(label_files)
    
    for label_file in label_files:
        try:
            # 检查对应的图像文件是否存在
            image_name = label_file.stem
            image_files = list(image_dir.glob(f"{image_name}.*"))
            
            if not image_files:
                stats['missing_images'].append(str(label_file))
                continue
            
            # 验证标签内容
            with open(label_file, 'r') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    stats['invalid_files'].append(f"{label_file}:{line_num} - 格式错误")
                    continue
                
                try:
                    class_id = int(parts[0])
                    x, y, w, h = map(float, parts[1:])
                    
                    # 验证类别ID
                    if class_id not in [0, 1, 2]:
                        stats['invalid_files'].append(f"{label_file}:{line_num} - 无效类别ID: {class_id}")
                        continue
                    
                    # 验证坐标范围
                    if not (0 <= x <= 1 and 0 <= y <= 1 and 0 <= w <= 1 and 0 <= h <= 1):
                        stats['invalid_files'].append(f"{label_file}:{line_num} - 坐标超出范围")
                        continue
                    
                    stats['class_counts'][class_id] += 1
                    stats['total_objects'] += 1
                    
                except ValueError:
                    stats['invalid_files'].append(f"{label_file}:{line_num} - 数值格式错误")
        
        except Exception as e:
            stats['invalid_files'].append(f"{label_file} - 读取错误: {e}")
    
    # 检查缺失的标签文件
    image_files = list(image_dir.glob("*"))
    for image_file in image_files:
        if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
            label_file = label_dir / f"{image_file.stem}.txt"
            if not label_file.exists():
                stats['missing_labels'].append(str(image_file))
    
    return stats


def print_validation_report(train_stats: Dict[str, Any], val_stats: Dict[str, Any]):
    """打印验证报告"""
    logger.info("=" * 60)
    logger.info("📊 数据集验证报告")
    logger.info("=" * 60)
    
    # 训练集统计
    logger.info("🏋️ 训练集:")
    logger.info(f"  标签文件数: {train_stats['total_labels']}")
    logger.info(f"  总对象数: {train_stats['total_objects']}")
    logger.info(f"  类别分布:")
    logger.info(f"    hand (0): {train_stats['class_counts'][0]}")
    logger.info(f"    product (1): {train_stats['class_counts'][1]}")
    logger.info(f"    target_container (2): {train_stats['class_counts'][2]}")
    
    # 验证集统计
    logger.info("🔍 验证集:")
    logger.info(f"  标签文件数: {val_stats['total_labels']}")
    logger.info(f"  总对象数: {val_stats['total_objects']}")
    logger.info(f"  类别分布:")
    logger.info(f"    hand (0): {val_stats['class_counts'][0]}")
    logger.info(f"    product (1): {val_stats['class_counts'][1]}")
    logger.info(f"    target_container (2): {val_stats['class_counts'][2]}")
    
    # 问题报告
    all_invalid = train_stats['invalid_files'] + val_stats['invalid_files']
    all_missing_images = train_stats['missing_images'] + val_stats['missing_images']
    all_missing_labels = train_stats['missing_labels'] + val_stats['missing_labels']
    
    if all_invalid:
        logger.warning(f"⚠️ 发现 {len(all_invalid)} 个无效标签:")
        for invalid in all_invalid[:10]:  # 只显示前10个
            logger.warning(f"  {invalid}")
        if len(all_invalid) > 10:
            logger.warning(f"  ... 还有 {len(all_invalid) - 10} 个")
    
    if all_missing_images:
        logger.warning(f"⚠️ 发现 {len(all_missing_images)} 个缺失图像:")
        for missing in all_missing_images[:5]:
            logger.warning(f"  {missing}")
        if len(all_missing_images) > 5:
            logger.warning(f"  ... 还有 {len(all_missing_images) - 5} 个")
    
    if all_missing_labels:
        logger.warning(f"⚠️ 发现 {len(all_missing_labels)} 个缺失标签:")
        for missing in all_missing_labels[:5]:
            logger.warning(f"  {missing}")
        if len(all_missing_labels) > 5:
            logger.warning(f"  ... 还有 {len(all_missing_labels) - 5} 个")
    
    # 建议
    total_objects = train_stats['total_objects'] + val_stats['total_objects']
    if total_objects == 0:
        logger.error("❌ 没有找到任何标注数据，请添加标注文件")
    elif total_objects < 300:
        logger.warning("⚠️ 数据量较少，建议至少300个标注对象以获得更好的训练效果")
    else:
        logger.success("✅ 数据集验证通过，可以开始训练")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据集准备和验证工具")
    parser.add_argument("--create", action="store_true", help="创建示例数据集结构")
    parser.add_argument("--validate", action="store_true", help="验证数据集")
    parser.add_argument("--data-dir", default="data", help="数据集目录")
    
    args = parser.parse_args()
    
    if args.create:
        create_sample_dataset()
    
    if args.validate:
        data_dir = Path(args.data_dir)
        
        if not data_dir.exists():
            logger.error(f"数据集目录不存在: {data_dir}")
            return 1
        
        # 验证训练集和验证集
        train_stats = validate_yolo_labels(
            data_dir / "train/labels",
            data_dir / "train/images"
        )
        
        val_stats = validate_yolo_labels(
            data_dir / "val/labels", 
            data_dir / "val/images"
        )
        
        print_validation_report(train_stats, val_stats)
    
    if not args.create and not args.validate:
        logger.info("请使用 --create 创建数据集结构或 --validate 验证数据集")
        logger.info("使用 --help 查看详细帮助")
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
