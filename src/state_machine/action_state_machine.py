"""
动作检测状态机
实现基于状态转移的动作识别逻辑
"""

import time
from enum import Enum
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger

from ..utils.geometry import calculate_iou


class ActionState(Enum):
    """动作状态枚举"""
    IDLE = "idle"                           # 空闲状态
    TRACKING_HELD_OBJECT = "tracking_held"  # 跟踪已持有对象状态
    EVALUATING_RELEASE = "evaluating"       # 评估释放状态


@dataclass
class TrackedObject:
    """被跟踪的对象信息"""
    bbox: List[float]           # 边界框 [x1, y1, x2, y2]
    confidence: float           # 置信度
    first_seen_time: float      # 首次检测时间
    last_seen_time: float       # 最后检测时间
    class_name: str             # 类别名称


class ActionStateMachine:
    """动作检测状态机"""
    
    def __init__(self, 
                 iou_held_threshold: float = 0.3,
                 iou_in_container_threshold: float = 0.5,
                 iou_release_threshold: float = 0.2,
                 cooldown_seconds: float = 3.0,
                 min_tracking_time: float = 0.5):
        """
        初始化状态机
        
        Args:
            iou_held_threshold: 手持有产品的IoU阈值
            iou_in_container_threshold: 产品进入容器的IoU阈值
            iou_release_threshold: 手与产品分离的IoU阈值
            cooldown_seconds: 动作触发后的冷却时间
            min_tracking_time: 最小跟踪时间（秒）
        """
        self.iou_held_threshold = iou_held_threshold
        self.iou_in_container_threshold = iou_in_container_threshold
        self.iou_release_threshold = iou_release_threshold
        self.cooldown_seconds = cooldown_seconds
        self.min_tracking_time = min_tracking_time
        
        # 状态变量
        self.current_state = ActionState.IDLE
        self.tracked_object: Optional[TrackedObject] = None
        self.last_action_time = 0.0
        self.state_enter_time = time.time()
        
        # 统计信息
        self.action_count = 0
        self.state_history = []
        
        logger.info(f"状态机初始化完成，初始状态: {self.current_state.value}")
    
    def update(self, detections: Dict[str, List[Dict[str, Any]]]) -> Optional[Dict[str, Any]]:
        """
        更新状态机
        
        Args:
            detections: 检测结果
            
        Returns:
            如果检测到动作完成，返回动作信息，否则返回None
        """
        current_time = time.time()
        
        # 检查冷却时间
        if current_time - self.last_action_time < self.cooldown_seconds:
            return None
        
        # 根据当前状态处理
        if self.current_state == ActionState.IDLE:
            return self._handle_idle_state(detections, current_time)
        elif self.current_state == ActionState.TRACKING_HELD_OBJECT:
            return self._handle_tracking_state(detections, current_time)
        elif self.current_state == ActionState.EVALUATING_RELEASE:
            return self._handle_evaluating_state(detections, current_time)
        
        return None
    
    def _handle_idle_state(self, detections: Dict[str, List[Dict[str, Any]]], 
                          current_time: float) -> Optional[Dict[str, Any]]:
        """处理空闲状态"""
        hands = detections.get('hand', [])
        products = detections.get('product', [])
        
        if not hands or not products:
            return None
        
        # 寻找手与产品的关联
        for hand in hands:
            for product in products:
                iou = calculate_iou(hand['bbox'], product['bbox'])
                
                if iou >= self.iou_held_threshold:
                    # 发现手持有产品，切换到跟踪状态
                    self.tracked_object = TrackedObject(
                        bbox=product['bbox'],
                        confidence=product['conf'],
                        first_seen_time=current_time,
                        last_seen_time=current_time,
                        class_name='product'
                    )
                    
                    self._transition_to_state(ActionState.TRACKING_HELD_OBJECT, current_time)
                    logger.info(f"检测到手持有产品，IoU: {iou:.3f}")
                    break
            
            if self.current_state != ActionState.IDLE:
                break
        
        return None
    
    def _handle_tracking_state(self, detections: Dict[str, List[Dict[str, Any]]], 
                              current_time: float) -> Optional[Dict[str, Any]]:
        """处理跟踪已持有对象状态"""
        if self.tracked_object is None:
            self._transition_to_state(ActionState.IDLE, current_time)
            return None
        
        products = detections.get('product', [])
        containers = detections.get('target_container', [])
        
        # 更新被跟踪对象的位置
        best_match = self._find_best_match(self.tracked_object.bbox, products)
        
        if best_match is None:
            # 跟踪对象丢失，返回空闲状态
            logger.warning("跟踪对象丢失，返回空闲状态")
            self._transition_to_state(ActionState.IDLE, current_time)
            return None
        
        # 更新跟踪对象信息
        self.tracked_object.bbox = best_match['bbox']
        self.tracked_object.confidence = best_match['conf']
        self.tracked_object.last_seen_time = current_time
        
        # 检查产品是否进入容器
        if containers:
            for container in containers:
                iou = calculate_iou(self.tracked_object.bbox, container['bbox'])
                
                if iou >= self.iou_in_container_threshold:
                    # 产品进入容器，切换到评估释放状态
                    self._transition_to_state(ActionState.EVALUATING_RELEASE, current_time)
                    logger.info(f"产品进入容器，IoU: {iou:.3f}")
                    break
        
        return None
    
    def _handle_evaluating_state(self, detections: Dict[str, List[Dict[str, Any]]], 
                                current_time: float) -> Optional[Dict[str, Any]]:
        """处理评估释放状态"""
        if self.tracked_object is None:
            self._transition_to_state(ActionState.IDLE, current_time)
            return None
        
        hands = detections.get('hand', [])
        products = detections.get('product', [])
        containers = detections.get('target_container', [])
        
        # 更新被跟踪对象的位置
        best_match = self._find_best_match(self.tracked_object.bbox, products)
        
        if best_match is None:
            # 跟踪对象丢失，可能已经完成动作
            logger.warning("评估状态下跟踪对象丢失")
            self._transition_to_state(ActionState.IDLE, current_time)
            return None
        
        # 更新跟踪对象信息
        self.tracked_object.bbox = best_match['bbox']
        self.tracked_object.confidence = best_match['conf']
        self.tracked_object.last_seen_time = current_time
        
        # 检查释放条件
        # 1. 产品仍在容器内
        product_in_container = False
        if containers:
            for container in containers:
                iou = calculate_iou(self.tracked_object.bbox, container['bbox'])
                if iou >= self.iou_in_container_threshold:
                    product_in_container = True
                    break
        
        if not product_in_container:
            # 产品不在容器内，返回跟踪状态
            logger.info("产品离开容器，返回跟踪状态")
            self._transition_to_state(ActionState.TRACKING_HELD_OBJECT, current_time)
            return None
        
        # 2. 手与产品分离
        hand_released = True
        if hands:
            for hand in hands:
                iou = calculate_iou(hand['bbox'], self.tracked_object.bbox)
                if iou >= self.iou_release_threshold:
                    hand_released = False
                    break
        
        # 3. 检查最小跟踪时间
        tracking_duration = current_time - self.tracked_object.first_seen_time
        
        if hand_released and tracking_duration >= self.min_tracking_time:
            # 动作完成！
            action_info = self._create_action_info(current_time)
            self._transition_to_state(ActionState.IDLE, current_time)
            self.last_action_time = current_time
            self.action_count += 1
            
            logger.success(f"动作完成！第{self.action_count}次检测到动作")
            return action_info
        
        return None
    
    def _find_best_match(self, target_bbox: List[float], 
                        candidates: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        在候选对象中找到最佳匹配
        
        Args:
            target_bbox: 目标边界框
            candidates: 候选对象列表
            
        Returns:
            最佳匹配对象，如果没有找到返回None
        """
        if not candidates:
            return None
        
        best_match = None
        best_iou = 0.0
        
        for candidate in candidates:
            iou = calculate_iou(target_bbox, candidate['bbox'])
            if iou > best_iou:
                best_iou = iou
                best_match = candidate
        
        # 只有IoU超过一定阈值才认为是匹配
        if best_iou >= 0.3:  # 可以调整这个阈值
            return best_match
        
        return None
    
    def _transition_to_state(self, new_state: ActionState, current_time: float) -> None:
        """状态转移"""
        old_state = self.current_state
        self.current_state = new_state
        self.state_enter_time = current_time
        
        # 记录状态历史
        self.state_history.append({
            'from_state': old_state.value,
            'to_state': new_state.value,
            'timestamp': current_time
        })
        
        # 状态转移时的清理工作
        if new_state == ActionState.IDLE:
            self.tracked_object = None
        
        logger.debug(f"状态转移: {old_state.value} -> {new_state.value}")
    
    def _create_action_info(self, current_time: float) -> Dict[str, Any]:
        """创建动作信息"""
        if self.tracked_object is None:
            return {}
        
        return {
            'action_id': self.action_count + 1,
            'timestamp': current_time,
            'tracked_object': {
                'bbox': self.tracked_object.bbox,
                'confidence': self.tracked_object.confidence,
                'tracking_duration': current_time - self.tracked_object.first_seen_time,
                'class_name': self.tracked_object.class_name
            },
            'state_machine_info': {
                'total_actions': self.action_count + 1,
                'last_action_time': self.last_action_time,
                'cooldown_remaining': max(0, self.cooldown_seconds - (current_time - self.last_action_time))
            }
        }
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取状态机信息"""
        current_time = time.time()
        
        info = {
            'current_state': self.current_state.value,
            'state_duration': current_time - self.state_enter_time,
            'action_count': self.action_count,
            'last_action_time': self.last_action_time,
            'cooldown_remaining': max(0, self.cooldown_seconds - (current_time - self.last_action_time)),
            'thresholds': {
                'iou_held': self.iou_held_threshold,
                'iou_in_container': self.iou_in_container_threshold,
                'iou_release': self.iou_release_threshold,
                'cooldown_seconds': self.cooldown_seconds,
                'min_tracking_time': self.min_tracking_time
            }
        }
        
        if self.tracked_object is not None:
            info['tracked_object'] = {
                'bbox': self.tracked_object.bbox,
                'confidence': self.tracked_object.confidence,
                'tracking_duration': current_time - self.tracked_object.first_seen_time,
                'class_name': self.tracked_object.class_name
            }
        
        return info
    
    def reset(self) -> None:
        """重置状态机"""
        self.current_state = ActionState.IDLE
        self.tracked_object = None
        self.state_enter_time = time.time()
        logger.info("状态机已重置")
