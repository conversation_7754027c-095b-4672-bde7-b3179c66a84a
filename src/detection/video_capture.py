"""
视频捕获模块
负责从摄像头或视频文件读取帧
"""

import cv2
import time
import numpy as np
from typing import Optional, Tuple, Union, Iterator
from pathlib import Path
from loguru import logger


class VideoCapture:
    """视频捕获类"""
    
    def __init__(self, source: Union[int, str], fps_limit: Optional[int] = None):
        """
        初始化视频捕获
        
        Args:
            source: 视频源，可以是摄像头ID(int)或视频文件路径(str)
            fps_limit: 帧率限制，None表示不限制
        """
        self.source = source
        self.fps_limit = fps_limit
        self.cap = None
        self.is_camera = isinstance(source, int)
        self.frame_count = 0
        self.start_time = None
        self.last_frame_time = 0
        
        self._initialize_capture()
    
    def _initialize_capture(self) -> None:
        """初始化视频捕获对象"""
        try:
            if self.is_camera:
                logger.info(f"初始化摄像头: {self.source}")
                self.cap = cv2.VideoCapture(self.source)
                
                # 设置摄像头参数
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                self.cap.set(cv2.CAP_PROP_FPS, 30)
                
            else:
                video_path = Path(self.source)
                if not video_path.exists():
                    raise FileNotFoundError(f"视频文件不存在: {self.source}")
                
                logger.info(f"初始化视频文件: {self.source}")
                self.cap = cv2.VideoCapture(str(video_path))
            
            if not self.cap.isOpened():
                raise RuntimeError(f"无法打开视频源: {self.source}")
            
            # 获取视频信息
            self._log_video_info()
            
        except Exception as e:
            logger.error(f"初始化视频捕获失败: {e}")
            raise
    
    def _log_video_info(self) -> None:
        """记录视频信息"""
        if self.cap is None:
            return
        
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        logger.info(f"视频信息 - 分辨率: {width}x{height}, FPS: {fps:.2f}")
        
        if not self.is_camera:
            frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            logger.info(f"视频时长: {duration:.2f}秒, 总帧数: {frame_count}")
    
    def read_frame(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        读取一帧
        
        Returns:
            (success, frame): 是否成功读取和帧数据
        """
        if self.cap is None:
            return False, None
        
        # 帧率限制
        if self.fps_limit is not None:
            current_time = time.time()
            frame_interval = 1.0 / self.fps_limit
            
            if current_time - self.last_frame_time < frame_interval:
                time.sleep(frame_interval - (current_time - self.last_frame_time))
            
            self.last_frame_time = time.time()
        
        success, frame = self.cap.read()
        
        if success:
            self.frame_count += 1
            if self.start_time is None:
                self.start_time = time.time()
        
        return success, frame
    
    def get_frame_generator(self) -> Iterator[np.ndarray]:
        """
        获取帧生成器
        
        Yields:
            视频帧
        """
        while True:
            success, frame = self.read_frame()
            if not success:
                if self.is_camera:
                    logger.warning("摄像头读取失败，尝试重新初始化")
                    self.release()
                    time.sleep(1)
                    self._initialize_capture()
                    continue
                else:
                    logger.info("视频文件读取完毕")
                    break
            
            yield frame
    
    def get_video_info(self) -> dict:
        """
        获取视频信息
        
        Returns:
            视频信息字典
        """
        if self.cap is None:
            return {}
        
        info = {
            'source': self.source,
            'is_camera': self.is_camera,
            'width': int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': self.cap.get(cv2.CAP_PROP_FPS),
            'frame_count': self.frame_count,
            'fps_limit': self.fps_limit
        }
        
        if not self.is_camera:
            info['total_frames'] = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            info['duration'] = info['total_frames'] / info['fps'] if info['fps'] > 0 else 0
        
        if self.start_time is not None:
            info['runtime'] = time.time() - self.start_time
            info['actual_fps'] = self.frame_count / info['runtime'] if info['runtime'] > 0 else 0
        
        return info
    
    def set_position(self, frame_number: int) -> bool:
        """
        设置视频位置（仅对视频文件有效）
        
        Args:
            frame_number: 目标帧号
            
        Returns:
            是否设置成功
        """
        if self.cap is None or self.is_camera:
            return False
        
        return self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
    
    def get_current_position(self) -> int:
        """
        获取当前帧位置
        
        Returns:
            当前帧号
        """
        if self.cap is None:
            return -1
        
        return int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
    
    def is_opened(self) -> bool:
        """
        检查视频捕获是否打开
        
        Returns:
            是否打开
        """
        return self.cap is not None and self.cap.isOpened()
    
    def release(self) -> None:
        """释放视频捕获资源"""
        if self.cap is not None:
            self.cap.release()
            self.cap = None
            logger.info("视频捕获资源已释放")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()
    
    def __del__(self):
        """析构函数"""
        self.release()
