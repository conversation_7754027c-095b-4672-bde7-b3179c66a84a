"""
日志配置工具
"""

import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(log_file: Optional[str] = None, 
                level: str = "INFO",
                rotation: str = "10 MB",
                retention: str = "7 days") -> None:
    """
    设置日志系统
    
    Args:
        log_file: 日志文件路径
        level: 日志级别
        rotation: 日志轮转大小
        retention: 日志保留时间
    """
    # 移除默认的控制台处理器
    logger.remove()
    
    # 添加控制台处理器，带颜色和格式
    logger.add(
        sys.stdout,
        level=level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
                   "{name}:{function}:{line} | {message}",
            rotation=rotation,
            retention=retention,
            encoding="utf-8"
        )
    
    logger.info(f"日志系统初始化完成，级别: {level}")
    if log_file:
        logger.info(f"日志文件: {log_file}")


def get_logger(name: str):
    """
    获取指定名称的logger
    
    Args:
        name: logger名称
        
    Returns:
        logger实例
    """
    return logger.bind(name=name)
