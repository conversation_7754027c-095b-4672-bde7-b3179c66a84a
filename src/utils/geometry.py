"""
几何计算工具函数
包括IoU计算、边界框操作等
"""

import numpy as np
from typing import List, Tuple, Union


def calculate_iou(box1: List[float], box2: List[float]) -> float:
    """
    计算两个边界框的交并比(IoU)
    
    Args:
        box1: 第一个边界框 [x1, y1, x2, y2]
        box2: 第二个边界框 [x1, y1, x2, y2]
        
    Returns:
        IoU值 (0-1之间)
    """
    # 确保输入是numpy数组
    box1 = np.array(box1)
    box2 = np.array(box2)
    
    # 计算交集区域的坐标
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    # 如果没有交集，返回0
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    # 计算交集面积
    intersection = (x2 - x1) * (y2 - y1)
    
    # 计算两个边界框的面积
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    
    # 计算并集面积
    union = area1 + area2 - intersection
    
    # 避免除零错误
    if union == 0:
        return 0.0
    
    return intersection / union


def box_center(box: List[float]) -> Tuple[float, float]:
    """
    计算边界框的中心点
    
    Args:
        box: 边界框 [x1, y1, x2, y2]
        
    Returns:
        中心点坐标 (cx, cy)
    """
    cx = (box[0] + box[2]) / 2
    cy = (box[1] + box[3]) / 2
    return cx, cy


def box_area(box: List[float]) -> float:
    """
    计算边界框的面积
    
    Args:
        box: 边界框 [x1, y1, x2, y2]
        
    Returns:
        面积
    """
    return (box[2] - box[0]) * (box[3] - box[1])


def box_distance(box1: List[float], box2: List[float]) -> float:
    """
    计算两个边界框中心点之间的欧几里得距离
    
    Args:
        box1: 第一个边界框 [x1, y1, x2, y2]
        box2: 第二个边界框 [x1, y1, x2, y2]
        
    Returns:
        距离
    """
    cx1, cy1 = box_center(box1)
    cx2, cy2 = box_center(box2)
    
    return np.sqrt((cx1 - cx2) ** 2 + (cy1 - cy2) ** 2)


def is_box_inside(inner_box: List[float], outer_box: List[float], 
                  threshold: float = 0.8) -> bool:
    """
    判断一个边界框是否在另一个边界框内部
    
    Args:
        inner_box: 内部边界框 [x1, y1, x2, y2]
        outer_box: 外部边界框 [x1, y1, x2, y2]
        threshold: IoU阈值，超过此值认为在内部
        
    Returns:
        是否在内部
    """
    iou = calculate_iou(inner_box, outer_box)
    return iou >= threshold


def expand_box(box: List[float], factor: float = 1.1) -> List[float]:
    """
    扩展边界框
    
    Args:
        box: 原始边界框 [x1, y1, x2, y2]
        factor: 扩展因子
        
    Returns:
        扩展后的边界框
    """
    cx, cy = box_center(box)
    w = box[2] - box[0]
    h = box[3] - box[1]
    
    new_w = w * factor
    new_h = h * factor
    
    x1 = cx - new_w / 2
    y1 = cy - new_h / 2
    x2 = cx + new_w / 2
    y2 = cy + new_h / 2
    
    return [x1, y1, x2, y2]


def clip_box(box: List[float], img_width: int, img_height: int) -> List[float]:
    """
    将边界框裁剪到图像范围内
    
    Args:
        box: 边界框 [x1, y1, x2, y2]
        img_width: 图像宽度
        img_height: 图像高度
        
    Returns:
        裁剪后的边界框
    """
    x1 = max(0, min(box[0], img_width))
    y1 = max(0, min(box[1], img_height))
    x2 = max(0, min(box[2], img_width))
    y2 = max(0, min(box[3], img_height))
    
    return [x1, y1, x2, y2]
