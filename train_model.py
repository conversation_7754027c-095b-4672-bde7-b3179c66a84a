#!/usr/bin/env python3
"""
YOLOv8模型训练脚本
用于训练自定义的动作检测模型
"""

import argparse
import sys
from pathlib import Path
from loguru import logger

try:
    from ultralytics import YOLO
except ImportError:
    logger.error("ultralytics未安装，请运行: pip install ultralytics")
    sys.exit(1)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="YOLOv8模型训练脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python train_model.py                    # 使用默认配置训练
  python train_model.py --epochs 200      # 训练200个epoch
  python train_model.py --batch 32        # 使用批次大小32
  python train_model.py --model yolov8m   # 使用YOLOv8m作为基础模型
  python train_model.py --resume          # 恢复上次训练
        """
    )
    
    parser.add_argument(
        "--data",
        type=str,
        default="data.yaml",
        help="数据集配置文件路径 (默认: data.yaml)"
    )
    
    parser.add_argument(
        "--model",
        type=str,
        default="yolov8n.pt",
        help="基础模型 (默认: yolov8n.pt)"
    )
    
    parser.add_argument(
        "--epochs",
        type=int,
        default=100,
        help="训练轮数 (默认: 100)"
    )
    
    parser.add_argument(
        "--batch",
        type=int,
        default=16,
        help="批次大小 (默认: 16)"
    )
    
    parser.add_argument(
        "--imgsz",
        type=int,
        default=640,
        help="图像尺寸 (默认: 640)"
    )
    
    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        help="训练设备 (默认: auto)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=8,
        help="数据加载器工作进程数 (默认: 8)"
    )
    
    parser.add_argument(
        "--project",
        type=str,
        default="runs/train",
        help="项目目录 (默认: runs/train)"
    )
    
    parser.add_argument(
        "--name",
        type=str,
        default="action_detection",
        help="实验名称 (默认: action_detection)"
    )
    
    parser.add_argument(
        "--resume",
        action="store_true",
        help="恢复上次训练"
    )
    
    parser.add_argument(
        "--pretrained",
        action="store_true",
        default=True,
        help="使用预训练权重 (默认: True)"
    )
    
    parser.add_argument(
        "--optimizer",
        type=str,
        default="auto",
        choices=["SGD", "Adam", "AdamW", "auto"],
        help="优化器 (默认: auto)"
    )
    
    parser.add_argument(
        "--lr0",
        type=float,
        default=0.01,
        help="初始学习率 (默认: 0.01)"
    )
    
    parser.add_argument(
        "--patience",
        type=int,
        default=50,
        help="早停耐心值 (默认: 50)"
    )
    
    return parser.parse_args()


def validate_data_structure(data_path: Path) -> bool:
    """验证数据集结构"""
    logger.info("验证数据集结构...")
    
    if not data_path.exists():
        logger.error(f"数据集配置文件不存在: {data_path}")
        return False
    
    # 检查数据目录结构
    data_dir = Path("data")
    required_dirs = [
        "train/images",
        "train/labels", 
        "val/images",
        "val/labels"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = data_dir / dir_path
        if not full_path.exists():
            missing_dirs.append(str(full_path))
    
    if missing_dirs:
        logger.warning("以下目录不存在，将自动创建:")
        for dir_path in missing_dirs:
            logger.warning(f"  - {dir_path}")
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    # 检查是否有训练数据
    train_images = list((data_dir / "train/images").glob("*"))
    val_images = list((data_dir / "val/images").glob("*"))
    
    if not train_images:
        logger.warning("训练图像目录为空，请添加训练数据")
        logger.info("数据集目录结构应该如下:")
        logger.info("data/")
        logger.info("├── train/")
        logger.info("│   ├── images/  # 训练图像")
        logger.info("│   └── labels/  # 训练标签 (YOLO格式)")
        logger.info("└── val/")
        logger.info("    ├── images/  # 验证图像")
        logger.info("    └── labels/  # 验证标签 (YOLO格式)")
        return False
    
    logger.info(f"找到 {len(train_images)} 张训练图像")
    logger.info(f"找到 {len(val_images)} 张验证图像")
    
    return True


def main():
    """主函数"""
    args = parse_arguments()
    
    logger.info("=" * 60)
    logger.info("🚀 YOLOv8模型训练开始")
    logger.info("=" * 60)
    
    # 验证数据集
    data_path = Path(args.data)
    if not validate_data_structure(data_path):
        logger.error("数据集验证失败，请检查数据集结构")
        return 1
    
    try:
        # 加载模型
        logger.info(f"加载基础模型: {args.model}")
        model = YOLO(args.model)
        
        # 显示训练配置
        logger.info("训练配置:")
        logger.info(f"  数据集: {args.data}")
        logger.info(f"  基础模型: {args.model}")
        logger.info(f"  训练轮数: {args.epochs}")
        logger.info(f"  批次大小: {args.batch}")
        logger.info(f"  图像尺寸: {args.imgsz}")
        logger.info(f"  设备: {args.device}")
        logger.info(f"  优化器: {args.optimizer}")
        logger.info(f"  学习率: {args.lr0}")
        logger.info(f"  早停耐心: {args.patience}")
        
        # 开始训练
        logger.info("开始训练...")
        results = model.train(
            data=str(data_path),
            epochs=args.epochs,
            batch=args.batch,
            imgsz=args.imgsz,
            device=args.device,
            workers=args.workers,
            project=args.project,
            name=args.name,
            resume=args.resume,
            pretrained=args.pretrained,
            optimizer=args.optimizer,
            lr0=args.lr0,
            patience=args.patience,
            save=True,
            save_period=10,  # 每10个epoch保存一次
            plots=True,
            verbose=True
        )
        
        logger.success("训练完成！")
        
        # 显示结果
        if hasattr(results, 'results_dict'):
            logger.info("训练结果:")
            for key, value in results.results_dict.items():
                logger.info(f"  {key}: {value}")
        
        # 模型保存路径
        save_dir = Path(args.project) / args.name
        best_model = save_dir / "weights" / "best.pt"
        last_model = save_dir / "weights" / "last.pt"
        
        if best_model.exists():
            logger.success(f"最佳模型已保存: {best_model}")
        if last_model.exists():
            logger.info(f"最后模型已保存: {last_model}")
        
        # 建议下一步操作
        logger.info("下一步操作:")
        logger.info(f"1. 将最佳模型复制到 models/ 目录:")
        logger.info(f"   cp {best_model} models/best.pt")
        logger.info("2. 更新 config.yaml 中的模型路径")
        logger.info("3. 运行主程序测试模型:")
        logger.info("   python main.py")
        
    except Exception as e:
        logger.error(f"训练过程中出错: {e}")
        logger.exception("详细错误信息:")
        return 1
    
    logger.info("=" * 60)
    logger.info("🎉 训练流程完成")
    logger.info("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
