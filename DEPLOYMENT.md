# 部署指南

## 系统要求

### 硬件要求
- **GPU**: NVIDIA CUDA支持的GPU（推荐显存≥6GB）
- **CPU**: 多核处理器（推荐4核以上）
- **内存**: 8GB RAM（推荐16GB）
- **存储**: 至少10GB可用空间

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+), Windows 10+, macOS 10.14+
- **Python**: 3.8-3.11
- **CUDA**: 11.0+ (如果使用GPU)
- **Conda**: Anaconda或Miniconda

## 快速部署

### 1. 环境设置

```bash
# 克隆或下载项目
cd /path/to/yolo_detect_ws

# 运行环境设置脚本
bash scripts/setup_environment.sh

# 激活环境
conda activate yolo_detect
```

### 2. 验证安装

```bash
# 运行系统测试
python scripts/test_system.py

# 检查配置
python -c "from src.config import ConfigManager; print('配置系统正常')"
```

### 3. 准备数据集（如需训练自定义模型）

```bash
# 创建数据集结构
python scripts/prepare_dataset.py --create

# 添加您的图像和标签到data/目录
# 验证数据集
python scripts/prepare_dataset.py --validate
```

### 4. 运行系统

```bash
# 使用默认配置运行
python main.py

# 使用摄像头
python main.py --camera 0

# 使用视频文件
python main.py --video path/to/video.mp4

# 不显示窗口（服务器模式）
python main.py --no-display
```

## 配置说明

### 主配置文件 (config.yaml)

```yaml
# 模型配置
model:
  path: "models/best.pt"        # 模型路径
  conf_threshold: 0.5           # 置信度阈值
  img_size: 640                 # 图像尺寸

# 视频源配置
video:
  source: 0                     # 摄像头ID或视频文件路径
  fps_limit: 30                 # 帧率限制

# 动作检测阈值
thresholds:
  iou_held: 0.3                 # 手持有产品的IoU阈值
  iou_in_container: 0.5         # 产品进入容器的IoU阈值
  iou_release: 0.2              # 手与产品分离的IoU阈值
  cooldown_seconds: 3.0         # 冷却时间

# 输出配置
output:
  save_images: true             # 是否保存检测图像
  image_save_path: "output/detected_actions"
  log_file: "logs/action_detection.log"
  show_video: true              # 是否显示视频窗口
```

## 模型训练

### 1. 准备数据集

数据集应按以下结构组织：

```
data/
├── train/
│   ├── images/     # 训练图像
│   └── labels/     # YOLO格式标签
└── val/
    ├── images/     # 验证图像
    └── labels/     # YOLO格式标签
```

### 2. 标签格式

每个图像对应一个.txt标签文件，格式为：
```
class_id center_x center_y width height
```

类别映射：
- 0: hand (手部)
- 1: product (产品)
- 2: target_container (目标容器)

### 3. 开始训练

```bash
# 使用默认配置训练
python train_model.py

# 自定义参数
python train_model.py --epochs 200 --batch 32 --model yolov8m.pt
```

### 4. 使用训练好的模型

```bash
# 复制最佳模型
cp runs/train/action_detection/weights/best.pt models/

# 更新配置文件中的模型路径
# 运行检测
python main.py
```

## 性能优化

### GPU加速

确保安装了CUDA版本的PyTorch：
```bash
# 检查CUDA可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# 如果需要，安装CUDA版本的PyTorch
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 模型选择

根据性能需求选择合适的模型：
- **YOLOv8n**: 最快，精度较低
- **YOLOv8s**: 平衡速度和精度
- **YOLOv8m**: 较高精度，速度适中
- **YOLOv8l**: 高精度，速度较慢
- **YOLOv8x**: 最高精度，最慢

### 参数调优

根据实际场景调整以下参数：
- `conf_threshold`: 降低可提高召回率，提高可减少误检
- `iou_*`: 根据实际动作特征调整IoU阈值
- `cooldown_seconds`: 根据动作频率调整冷却时间

## 故障排除

### 常见问题

1. **摄像头无法打开**
   ```bash
   # 检查摄像头设备
   ls /dev/video*
   # 尝试不同的摄像头ID
   python main.py --camera 1
   ```

2. **CUDA内存不足**
   ```bash
   # 减小批次大小
   python train_model.py --batch 8
   # 或使用CPU
   python main.py --device cpu
   ```

3. **模型加载失败**
   ```bash
   # 检查模型文件是否存在
   ls -la models/
   # 使用预训练模型
   python main.py --model yolov8n.pt
   ```

4. **依赖包问题**
   ```bash
   # 重新安装环境
   conda env remove -n yolo_detect
   bash scripts/setup_environment.sh
   ```

### 日志分析

检查日志文件获取详细错误信息：
```bash
tail -f logs/action_detection.log
```

## 生产部署

### 服务器部署

1. **无头模式运行**
   ```bash
   python main.py --no-display --log-level INFO
   ```

2. **系统服务配置**
   创建systemd服务文件：
   ```ini
   [Unit]
   Description=YOLO Action Detection Service
   After=network.target

   [Service]
   Type=simple
   User=your_user
   WorkingDirectory=/path/to/yolo_detect_ws
   ExecStart=/path/to/conda/envs/yolo_detect/bin/python main.py --no-display
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

3. **Docker部署**
   ```dockerfile
   FROM nvidia/cuda:11.8-runtime-ubuntu20.04
   
   # 安装依赖
   RUN apt-get update && apt-get install -y python3 python3-pip
   
   # 复制项目文件
   COPY . /app
   WORKDIR /app
   
   # 安装Python依赖
   RUN pip3 install -r requirements.txt
   
   # 运行应用
   CMD ["python3", "main.py", "--no-display"]
   ```

### 监控和维护

1. **性能监控**
   - 监控CPU/GPU使用率
   - 监控内存使用情况
   - 监控检测准确率

2. **日志轮转**
   配置logrotate处理日志文件

3. **定期维护**
   - 清理输出图像
   - 更新模型权重
   - 检查系统性能

## 技术支持

如遇到问题，请提供以下信息：
1. 系统环境信息
2. 错误日志
3. 配置文件
4. 复现步骤

联系方式：[添加您的联系信息]
