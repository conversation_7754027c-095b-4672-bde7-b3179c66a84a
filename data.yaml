# YOLOv8数据集配置文件
# 用于训练自定义的动作检测模型

# 数据集路径
path: ./data  # 数据集根目录
train: train/images  # 训练图像目录 (相对于path)
val: val/images      # 验证图像目录 (相对于path)

# 类别数量
nc: 3

# 类别名称
names:
  0: hand              # 手部
  1: product           # 产品
  2: target_container  # 目标容器

# 数据集信息
dataset_info:
  description: "动作检测数据集 - 检测操作员将产品放入容器的动作"
  version: "1.0"
  created: "2024"
  
# 训练配置建议
training_config:
  epochs: 100
  batch_size: 16
  img_size: 640
  patience: 50
  
# 数据增强配置
augmentation:
  hsv_h: 0.015    # 色调增强
  hsv_s: 0.7      # 饱和度增强
  hsv_v: 0.4      # 明度增强
  degrees: 0.0    # 旋转角度
  translate: 0.1  # 平移
  scale: 0.5      # 缩放
  shear: 0.0      # 剪切
  perspective: 0.0 # 透视变换
  flipud: 0.0     # 垂直翻转
  fliplr: 0.5     # 水平翻转
  mosaic: 1.0     # 马赛克增强
  mixup: 0.0      # 混合增强
