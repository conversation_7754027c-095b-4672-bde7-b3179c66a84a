#!/usr/bin/env python3
"""
检测测试脚本 - 诊断YOLO检测问题
"""

import cv2
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ultralytics import YOLO
    print("✅ ultralytics 已安装")
except ImportError:
    print("❌ ultralytics 未安装，请运行: pip install ultralytics")
    sys.exit(1)

def test_basic_yolo():
    """测试基础YOLO功能"""
    print("\n🔍 测试基础YOLO检测...")
    
    # 使用预训练模型
    model = YOLO('yolov8n.pt')
    print(f"✅ 模型加载成功")
    print(f"📋 模型类别数量: {len(model.names)}")
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ 无法打开摄像头")
        return
    
    print("📹 摄像头已打开，开始检测...")
    print("按 'q' 退出")
    
    frame_count = 0
    detection_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 执行检测
        results = model(frame, conf=0.25, verbose=False)
        
        # 检查检测结果
        if results[0].boxes is not None and len(results[0].boxes) > 0:
            detection_count += 1
            num_detections = len(results[0].boxes)
            print(f"帧 {frame_count}: 检测到 {num_detections} 个对象")
        
        # 绘制结果
        annotated_frame = results[0].plot()
        
        # 添加统计信息
        info_text = f"Frame: {frame_count}, Detections: {detection_count}"
        cv2.putText(annotated_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow("Basic YOLO Test", annotated_frame)
        
        # 检查退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
        
        # 测试50帧就够了
        if frame_count >= 50:
            break
    
    cap.release()
    cv2.destroyAllWindows()
    
    detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
    print(f"\n📊 测试结果:")
    print(f"   总帧数: {frame_count}")
    print(f"   有检测的帧数: {detection_count}")
    print(f"   检测率: {detection_rate:.1f}%")
    
    return detection_count > 0

if __name__ == "__main__":
    print("🚀 YOLO检测测试")
    print("=" * 50)
    
    success = test_basic_yolo()
    
    if success:
        print("\n✅ 基础YOLO检测正常工作！")
        print("💡 问题可能在于：")
        print("   1. 自定义模型的类别不匹配")
        print("   2. 置信度阈值设置过高")
        print("   3. 模型文件路径错误")
        print("\n🔧 建议的解决方案：")
        print("   python main.py --debug --conf-threshold 0.25")
    else:
        print("\n❌ 基础YOLO检测也有问题")
        print("💡 可能的原因：")
        print("   1. 摄像头无法正常工作")
        print("   2. 环境中没有可检测的对象")
        print("   3. ultralytics安装有问题")
    
    print("\n✅ 测试完成！")
