#!/usr/bin/env python3
"""
YOLO检测调试脚本
用于测试YOLO模型是否能正常检测对象
"""

import cv2
import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.detection.detector import YOLODetector
from src.config import ConfigManager
from loguru import logger

def test_yolo_detection():
    """测试YOLO检测功能"""
    
    # 加载配置
    config = ConfigManager("config.yaml")
    
    # 创建检测器
    logger.info("初始化YOLO检测器...")
    detector = YOLODetector(
        model_path=config.model_path,
        conf_threshold=0.25,  # 降低置信度阈值以检测更多对象
        device='cpu'  # 强制使用CPU
    )
    
    # 打开摄像头或视频
    video_source = config.video_source
    logger.info(f"打开视频源: {video_source}")
    
    cap = cv2.VideoCapture(video_source)
    if not cap.isOpened():
        logger.error(f"无法打开视频源: {video_source}")
        return
    
    frame_count = 0
    detection_count = 0
    
    logger.info("开始检测测试...")
    logger.info("按 'q' 退出，按 's' 保存当前帧")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                logger.error("无法读取视频帧")
                break
            
            frame_count += 1
            
            # 执行检测
            detections = detector.detect(frame)
            
            # 统计检测结果
            total_objects = sum(len(objects) for objects in detections.values())
            if total_objects > 0:
                detection_count += 1
                logger.info(f"帧 {frame_count}: 检测到 {total_objects} 个对象")
                
                # 详细输出检测结果
                for class_name, objects in detections.items():
                    if objects:
                        logger.info(f"  {class_name}: {len(objects)} 个")
                        for i, obj in enumerate(objects):
                            bbox = obj['bbox']
                            conf = obj['conf']
                            logger.info(f"    [{i+1}] 置信度: {conf:.3f}, 边界框: [{bbox[0]:.0f}, {bbox[1]:.0f}, {bbox[2]:.0f}, {bbox[3]:.0f}]")
            
            # 绘制检测结果
            display_frame = detector.draw_detections(frame, detections, show_conf=True)
            
            # 添加信息文本
            info_text = f"Frame: {frame_count}, Detections: {total_objects}, Total frames with detections: {detection_count}"
            cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示结果
            cv2.imshow("YOLO Detection Test", display_frame)
            
            # 检查按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                logger.info("用户退出")
                break
            elif key == ord('s'):
                # 保存当前帧
                filename = f"debug_frame_{frame_count}_{total_objects}objects.jpg"
                cv2.imwrite(filename, display_frame)
                logger.info(f"保存帧: {filename}")
            
            # 每100帧输出一次统计
            if frame_count % 100 == 0:
                detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
                logger.info(f"统计: 处理了 {frame_count} 帧, 有检测的帧: {detection_count} ({detection_rate:.1f}%)")
    
    except KeyboardInterrupt:
        logger.info("用户中断")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        # 最终统计
        detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
        logger.info("=" * 50)
        logger.info("检测测试完成")
        logger.info(f"总帧数: {frame_count}")
        logger.info(f"有检测的帧数: {detection_count}")
        logger.info(f"检测率: {detection_rate:.1f}%")
        logger.info("=" * 50)

def test_model_info():
    """测试模型信息"""
    logger.info("测试模型加载...")
    
    config = ConfigManager("config.yaml")
    detector = YOLODetector(
        model_path=config.model_path,
        conf_threshold=0.25,
        device='cpu'
    )
    
    # 输出模型信息
    if hasattr(detector.model, 'names'):
        logger.info(f"模型类别: {detector.model.names}")
    
    if hasattr(detector.model, 'model'):
        logger.info(f"模型结构: {type(detector.model.model)}")
    
    logger.info(f"置信度阈值: {detector.conf_threshold}")
    logger.info(f"图像尺寸: {detector.img_size}")
    logger.info(f"设备: {detector.device}")

if __name__ == "__main__":
    logger.info("YOLO检测调试工具")
    logger.info("1. 测试模型信息")
    test_model_info()
    
    logger.info("\n2. 测试实时检测")
    test_yolo_detection()
