#!/usr/bin/env python3
"""
专门用于测试离线视频的YOLO检测脚本
"""

import sys
import cv2
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ultralytics import YOLO
except ImportError:
    print("❌ ultralytics未安装，请运行: pip install ultralytics")
    sys.exit(1)


def test_video_detection(video_path, model_path="scripts/models/yolov8s.pt", 
                        conf_threshold=0.5, save_output=False):
    """
    测试视频检测
    
    Args:
        video_path: 视频文件路径
        model_path: 模型文件路径
        conf_threshold: 置信度阈值
        save_output: 是否保存输出视频
    """
    print(f"🎬 开始测试视频: {video_path}")
    
    # 检查文件是否存在
    video_path = Path(video_path)
    model_path = Path(model_path)
    
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    # 加载模型
    print(f"📦 加载模型: {model_path}")
    try:
        model = YOLO(str(model_path))
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 打开视频
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ 无法打开视频: {video_path}")
        return False
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    duration = total_frames / fps if fps > 0 else 0
    
    print(f"📹 视频信息:")
    print(f"   分辨率: {width}x{height}")
    print(f"   帧率: {fps:.2f} FPS")
    print(f"   总帧数: {total_frames}")
    print(f"   时长: {duration:.2f}秒")
    
    # 设置输出视频（如果需要保存）
    out = None
    if save_output:
        output_path = f"output_{video_path.stem}_detected.mp4"
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        print(f"💾 将保存输出视频到: {output_path}")
    
    print("\n🎮 控制说明:")
    print("   - 按 'q' 退出")
    print("   - 按 's' 保存当前帧")
    print("   - 按 'SPACE' 暂停/继续")
    print("   - 按 'f' 快进")
    print("   - 按 'b' 后退")
    print("\n🔍 开始检测...")
    
    frame_count = 0
    paused = False
    detection_stats = {'total_detections': 0, 'frames_with_detections': 0}
    
    try:
        while True:
            if not paused:
                ret, frame = cap.read()
                if not ret:
                    print("📺 视频处理完毕")
                    break
                
                frame_count += 1
                current_time = frame_count / fps if fps > 0 else 0
                
                # 执行YOLO检测
                results = model(frame, conf=conf_threshold, verbose=False)
                
                # 统计检测结果
                detections = results[0].boxes
                if detections is not None and len(detections) > 0:
                    detection_stats['frames_with_detections'] += 1
                    detection_stats['total_detections'] += len(detections)
                
                # 绘制检测结果
                annotated_frame = results[0].plot()
                
                # 添加信息覆盖层
                info_lines = [
                    f"Frame: {frame_count}/{total_frames} ({frame_count/total_frames*100:.1f}%)",
                    f"Time: {current_time:.1f}s / {duration:.1f}s",
                    f"Detections: {len(detections) if detections is not None else 0}",
                    f"Total found: {detection_stats['total_detections']}"
                ]
                
                # 绘制信息文本
                y_start = 30
                for i, line in enumerate(info_lines):
                    y = y_start + i * 25
                    # 绘制文本背景
                    (text_width, text_height), _ = cv2.getTextSize(line, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)
                    cv2.rectangle(annotated_frame, (10, y-20), (10+text_width+10, y+5), (0, 0, 0), -1)
                    # 绘制文本
                    cv2.putText(annotated_frame, line, (15, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # 保存到输出视频
                if out is not None:
                    out.write(annotated_frame)
            
            # 显示当前帧
            cv2.imshow('YOLO视频检测测试', annotated_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("👋 用户退出")
                break
            elif key == ord('s'):
                # 保存当前帧
                save_path = f"frame_{frame_count:06d}_detections.jpg"
                cv2.imwrite(save_path, annotated_frame)
                print(f"💾 已保存帧: {save_path}")
            elif key == ord(' '):  # 暂停/继续
                paused = not paused
                print(f"⏸️ {'暂停' if paused else '继续'}")
            elif key == ord('f'):  # 快进
                new_pos = min(frame_count + 30, total_frames - 1)
                cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)
                frame_count = new_pos
                print(f"⏩ 快进到第{frame_count}帧")
            elif key == ord('b'):  # 后退
                new_pos = max(frame_count - 30, 0)
                cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)
                frame_count = new_pos
                print(f"⏪ 后退到第{frame_count}帧")
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    finally:
        # 清理资源
        cap.release()
        if out is not None:
            out.release()
        cv2.destroyAllWindows()
        
        # 输出统计信息
        print("\n📊 检测统计:")
        print(f"   处理帧数: {frame_count}")
        print(f"   有检测结果的帧数: {detection_stats['frames_with_detections']}")
        print(f"   检测率: {detection_stats['frames_with_detections']/frame_count*100:.1f}%")
        print(f"   总检测数: {detection_stats['total_detections']}")
        print(f"   平均每帧检测数: {detection_stats['total_detections']/frame_count:.2f}")
    
    return True


def main():
    parser = argparse.ArgumentParser(description="YOLO视频检测测试工具")
    parser.add_argument("--video", "-v", type=str, 
                       default="data/raw_videos/short_video_for_test.mp4",
                       help="视频文件路径")
    parser.add_argument("--model", "-m", type=str,
                       default="scripts/models/yolov8s.pt", 
                       help="模型文件路径")
    parser.add_argument("--conf", "-c", type=float, default=0.5,
                       help="置信度阈值")
    parser.add_argument("--save", "-s", action="store_true",
                       help="保存输出视频")
    
    args = parser.parse_args()
    
    print("🚀 YOLO视频检测测试工具")
    print("=" * 50)
    
    # 如果没有指定视频，尝试自动查找
    if args.video == "data/raw_videos/short_video_for_test.mp4":
        video_dir = Path("data/raw_videos")
        if video_dir.exists():
            video_files = list(video_dir.glob("*.mp4")) + list(video_dir.glob("*.avi"))
            if video_files:
                args.video = str(video_files[0])
                print(f"🎯 自动选择视频: {args.video}")
    
    success = test_video_detection(
        video_path=args.video,
        model_path=args.model,
        conf_threshold=args.conf,
        save_output=args.save
    )
    
    if success:
        print("\n🎉 视频检测测试完成!")
        print("💡 如果检测效果满意，可以运行完整的动作检测系统:")
        print(f"   python main.py --video {args.video}")
    else:
        print("\n❌ 视频检测测试失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
