# YOLOv8实时动作检测系统

基于YOLOv8与规则引擎的实时动作检测系统，用于检测"操作员将指定产品放入目标容器内"的动作。

## 系统架构

本系统由三个核心模块构成：

1. **模型训练模块（离线）**: 生成定制化的YOLOv8目标检测模型
2. **实时检测与分析模块（在线）**: 实时目标检测和结果结构化
3. **动作决策与响应模块（在线）**: 基于状态机的规则引擎

## 项目结构

```
yolo_detect_ws/
├── src/                    # 源代码目录
│   ├── detection/          # 检测模块
│   ├── state_machine/      # 状态机模块
│   ├── config/            # 配置管理模块
│   └── utils/             # 工具函数
├── models/                # 模型文件目录
├── data/                  # 数据集目录
│   ├── train/             # 训练数据
│   └── val/               # 验证数据
├── logs/                  # 日志文件
├── output/                # 输出结果
├── config.yaml           # 主配置文件
├── requirements.txt       # Python依赖
└── README.md             # 项目文档
```

## 安装和使用

### 1. 环境配置

```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 模型训练

```bash
# 准备数据集（按YOLO格式组织）
# 运行训练脚本
python train_model.py
```

### 3. 运行检测系统

```bash
# 使用默认配置运行
python main.py

# 使用自定义配置文件
python main.py --config custom_config.yaml
```

## 配置说明

主要配置参数在 `config.yaml` 中：

- `model.path`: 模型权重文件路径
- `video.source`: 视频源（摄像头ID或文件路径）
- `thresholds.iou_*`: 各种IoU阈值
- `output.*`: 输出相关配置

## 检测类别

系统检测以下三个核心类别：

1. `hand`: 操作员的手部
2. `product`: 被操作的产品
3. `target_container`: 目标容器

## 状态机逻辑

系统使用三状态状态机：

1. `IDLE`: 空闲状态，监控动作起始
2. `TRACKING_HELD_OBJECT`: 跟踪已持有对象
3. `EVALUATING_RELEASE`: 评估释放动作

## 运行环境要求

- **硬件**: NVIDIA CUDA支持的GPU（推荐显存≥6GB）
- **软件**: Python 3.8+, PyTorch, ultralytics, OpenCV

## 许可证

[添加许可证信息]
